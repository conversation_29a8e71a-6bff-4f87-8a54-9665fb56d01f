# PostgreSQL 数据库连接 URL，需本地安装或连接远程 PostgreSQL
# Docker 部署时无需填写
DATABASE_URL=
# 用于用户信息等敏感信息的加密，可以使用 openssl rand -base64 32 生成一个随机的 32 位字符串作为密钥。
# 以下为示例，生产环境请重新生成
AUTH_SECRET=PKqQmr74pyUXLR18kx85is9yXguIinaJ40DrOBim+Tg=
# 管理员授权码，安装完成后，凭此值访问 /setup 页设置管理员账号
ADMIN_CODE=11223344
# 本地开发时，用于访问服务器的端口，生产环境请结合 nginx 等反向代理设置
HOST_PORT=3000
# 生产环境设置为正式域名
NEXTAUTH_URL=http://localhost:${HOST_PORT}
AUTH_TRUST_HOST=true

# 是否开启邮箱登录，开启值设为 ON，关闭时修改为 OFF
EMAIL_AUTH_STATUS=ON

# 是否开启飞书登录，开启值设为 ON，关闭时修改为 OFF，详细说明见 README 底部附 2
FEISHU_AUTH_STATUS=OFF
FEISHU_CLIENT_ID="cli_a7343e4a4d78xxxx"
FEISHU_CLIENT_SECRET="ScEbex2ZHOEWIoE7eDc1Lhc0042OXXXX"

#是否开启企业微信登录，开启值设为 ON，关闭时修改为 OFF
WECOM_AUTH_STATUS=OFF
WECOM_CLIENT_ID="ww728c371c2fXXXXXX"
WECOM_AGENT_ID="100XXXX"
WECOM_CLIENT_SECRET="H-7J4jzG0m1axpXLGshaCDlMOZxdjvkX6bIVLuXXXXXX"

#是否开启钉钉登录，开启值设为 ON，关闭时修改为 OFF
DINGDING_AUTH_STATUS=OFF
DINGDING_CLIENT_ID="dingpcfi2kpuplXXXXXX"
DINGDING_CLIENT_SECRET="3vk9-VFCExNckqNUk_CL2F-HEgz7qGN-BimH0lZ1gUx6hWO7g_an2lnkk6XXXXXX"
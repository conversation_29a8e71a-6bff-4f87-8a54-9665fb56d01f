services:
  app:
    container_name: hivechat
    image: hivenexus/hivechat:latest
    ports:
      - "${HOST_PORT}:3000"
    depends_on:
      - db
    environment:
      DATABASE_URL: "************************************/hivechat"
      AUTH_SECRET: ${AUTH_SECRET}
      ADMIN_CODE: ${ADMIN_CODE}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
      AUTH_TRUST_HOST: ${AUTH_TRUST_HOST}
      EMAIL_AUTH_STATUS: ${EMAIL_AUTH_STATUS}
      FEISHU_AUTH_STATUS: ${FEISHU_AUTH_STATUS}
      FEISHU_CLIENT_ID: ${FEISHU_CLIENT_ID}
      FEISHU_CLIENT_SECRET: ${FEISHU_CLIENT_SECRET}
      WECOM_AUTH_STATUS: ${WECOM_AUTH_STATUS}
      WECOM_CLIENT_ID: ${WECOM_CLIENT_ID}
      WECOM_AGENT_ID: ${WECOM_AGENT_ID}
      WECOM_CLIENT_SECRET: ${WECOM_CLIENT_SECRET}
      DINGDING_AUTH_STATUS: ${DINGDING_AUTH_STATUS}
      DINGDING_CLIENT_ID: ${DINGDING_CLIENT_ID}
      DINGDING_CLIENT_SECRET: ${DINGDING_CLIENT_SECRET}
    restart: unless-stopped
    networks:
      - my-network

  db:
    container_name: hivechat-db
    image: postgres:16.8-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: hivechat
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker:/docker-entrypoint-initdb.d/
    restart: unless-stopped
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres -d hivechat" ]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - my-network

volumes:
  postgres_data:


networks:
  my-network:
    driver: bridge
